# 搜索词分析功能测试指南

## 测试概述

本指南将帮你全面测试搜索词分析系统的各个功能模块，确保系统正常运行。

## 1. 环境准备测试

### 1.1 数据库连接测试
```sql
-- 检查数据库连接
SELECT 1;

-- 验证表是否创建成功
SHOW TABLES LIKE 'ads_search_term%';

-- 检查表结构
DESCRIBE ads_search_term_analytics;
DESCRIBE ads_search_term_category_relation;
DESCRIBE ads_search_term_product_ranking;
```

### 1.2 应用启动测试
```bash
# 启动应用并检查日志
java -jar your-app.jar

# 检查关键组件是否加载
grep "AdsSearchTermAnalyticsService" logs/application.log
grep "SpAnalyticsReportsApi" logs/application.log
grep "AdsSearchTermAnalyticsTask" logs/application.log
```

## 2. 基础功能测试

### 2.1 配置验证测试
```bash
# 检查配置是否正确加载
curl -X GET "http://localhost:8080/actuator/configprops" | grep amazon.spapi
```

### 2.2 健康检查测试
```bash
# 应用健康检查
curl -X GET "http://localhost:8080/actuator/health"

# 数据库连接检查
curl -X GET "http://localhost:8080/actuator/health/db"
```

## 3. API接口测试

### 3.1 查询接口测试

#### 获取搜索词分析数据列表
```bash
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/list?accountId=TEST_ACCOUNT&profileId=123&pageNum=1&pageSize=10"
```

#### 获取搜索词详细信息
```bash
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/details?accountId=TEST_ACCOUNT&profileId=123&searchTerm=wireless%20headphones"
```

#### 获取搜索词关联类目
```bash
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/categories?accountId=TEST_ACCOUNT&profileId=123&searchTerm=wireless%20headphones"
```

#### 获取搜索词关联商品
```bash
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/products?accountId=TEST_ACCOUNT&profileId=123&searchTerm=wireless%20headphones"
```

### 3.2 同步接口测试

#### 手动同步搜索词数据
```bash
curl -X POST "http://localhost:8090/api/v1/ads/search-term-analytics/sync/analytics" \
  -d "accountId=TEST_ACCOUNT&profileId=123&marketplaceId=ATVPDKIKX0DER&startDate=2025-01-30&endDate=2025-01-30"
```

#### 完整数据同步
```bash
curl -X POST "http://localhost:8080/api/v1/ads/search-term-analytics/sync/all" \
  -d "accountId=TEST_ACCOUNT&profileId=123&marketplaceId=ATVPDKIKX0DER&startDate=2025-01-30&endDate=2025-01-30"
```

## 4. 数据库功能测试

### 4.1 插入测试数据
```sql
-- 插入测试搜索词数据
INSERT INTO ads_search_term_analytics (
    account_id, profile_id, marketplace_id, report_date, search_term,
    search_rank, monthly_search_volume, clicks, impressions,
    click_through_rate, conversion_rate, competition_intensity,
    data_source, status, create_time
) VALUES (
    'TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', '2025-01-31', 'wireless headphones',
    5, 50000, 1200, 15000, 8.0, 12.5, 2,
    'SP_API_SEARCH_QUERY_PERFORMANCE', 'ACTIVE', NOW()
);

-- 插入测试类目关联数据
INSERT INTO ads_search_term_category_relation (
    account_id, profile_id, marketplace_id, search_term, category_id,
    category_name, clicks_in_category, click_rank, is_primary_category,
    data_source, status, create_time
) VALUES (
    'TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 1001,
    'Electronics > Audio', 800, 1, 1,
    'SP_API_SEARCH_CATALOG_PERFORMANCE', 'ACTIVE', NOW()
);

-- 插入测试商品排名数据
INSERT INTO ads_search_term_product_ranking (
    account_id, profile_id, marketplace_id, search_term, asin,
    product_title, main_image_url, brand, price, rating,
    clicks, click_rank, data_source, status, create_time
) VALUES (
    'TEST_ACCOUNT', 123, 'ATVPDKIKX0DER', 'wireless headphones', 'B08TEST123',
    'Premium Wireless Headphones', 'https://example.com/image.jpg',
    'TestBrand', 99.99, 4.5, 500, 1,
    'SP_API_CATALOG_ITEMS', 'ACTIVE', NOW()
);
```

### 4.2 查询测试
```sql
-- 测试基本查询
SELECT * FROM ads_search_term_analytics WHERE account_id = 'TEST_ACCOUNT';

-- 测试关联查询
SELECT 
    a.search_term,
    a.monthly_search_volume,
    c.category_name,
    p.product_title
FROM ads_search_term_analytics a
LEFT JOIN ads_search_term_category_relation c ON a.search_term = c.search_term
LEFT JOIN ads_search_term_product_ranking p ON a.search_term = p.search_term
WHERE a.account_id = 'TEST_ACCOUNT';

-- 测试索引效果
EXPLAIN SELECT * FROM ads_search_term_analytics 
WHERE search_term LIKE 'wireless%';
```

## 5. 分表功能测试

### 5.1 分表创建测试
```sql
-- 测试当月分表创建
CALL CreateMonthlyTables('ads_search_term_analytics', CURDATE());
CALL CreateMonthlyTables('ads_search_term_category_relation', CURDATE());
CALL CreateMonthlyTables('ads_search_term_product_ranking', CURDATE());

-- 验证分表是否创建
SHOW TABLES LIKE 'ads_search_term_analytics_202501';
```

### 5.2 分表数据插入测试
```sql
-- 向分表插入数据
INSERT INTO ads_search_term_analytics_202501 
SELECT * FROM ads_search_term_analytics WHERE account_id = 'TEST_ACCOUNT';

-- 验证分表数据
SELECT COUNT(*) FROM ads_search_term_analytics_202501;
```

## 6. 定时任务测试

### 6.1 手动触发定时任务
```java
// 在测试类中手动触发
@Autowired
private AdsSearchTermAnalyticsTask analyticsTask;

@Test
public void testManualSync() {
    Map<String, Object> result = analyticsTask.manualSyncSearchTermData(
        "TEST_ACCOUNT", 123L, "ATVPDKIKX0DER", 
        "2025-01-30", "2025-01-30"
    );
    assertTrue((Boolean) result.get("success"));
}
```

### 6.2 定时任务日志检查
```bash
# 检查定时任务执行日志
grep "开始执行每日搜索词分析数据同步任务" logs/application.log
grep "搜索词分析数据同步任务完成" logs/application.log
```

## 7. 性能测试

### 7.1 数据量测试
```sql
-- 插入大量测试数据
INSERT INTO ads_search_term_analytics (
    account_id, profile_id, marketplace_id, report_date, search_term,
    monthly_search_volume, clicks, impressions, data_source, status, create_time
)
SELECT 
    'TEST_ACCOUNT',
    123,
    'ATVPDKIKX0DER',
    DATE_SUB(CURDATE(), INTERVAL FLOOR(RAND() * 30) DAY),
    CONCAT('test_search_term_', LPAD(seq, 6, '0')),
    FLOOR(RAND() * 100000),
    FLOOR(RAND() * 1000),
    FLOOR(RAND() * 10000),
    'TEST_DATA',
    'ACTIVE',
    NOW()
FROM (
    SELECT @row := @row + 1 as seq
    FROM information_schema.columns c1, information_schema.columns c2,
    (SELECT @row := 0) r
    LIMIT 10000
) t;
```

### 7.2 查询性能测试
```sql
-- 测试查询性能
SET profiling = 1;

SELECT * FROM ads_search_term_analytics 
WHERE account_id = 'TEST_ACCOUNT' 
  AND search_term LIKE 'test%' 
  AND report_date >= '2025-01-01';

SHOW PROFILES;
```

## 8. 错误处理测试

### 8.1 异常情况测试
```bash
# 测试无效参数
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/details?accountId=&profileId=&searchTerm="

# 测试不存在的数据
curl -X GET "http://localhost:8080/api/v1/ads/search-term-analytics/details?accountId=NONEXISTENT&profileId=999&searchTerm=nonexistent"
```

### 8.2 数据库连接异常测试
```java
// 模拟数据库连接异常
@Test
public void testDatabaseException() {
    // 停止数据库服务
    // 调用API接口
    // 验证错误处理
}
```

## 9. 集成测试

### 9.1 完整流程测试
```java
@Test
@Transactional
public void testCompleteWorkflow() {
    // 1. 同步搜索词数据
    Map<String, Object> syncResult = searchTermAnalyticsService.syncSearchTermAnalyticsData(
        "TEST_ACCOUNT", 123L, "ATVPDKIKX0DER", "2025-01-30", "2025-01-30"
    );
    assertTrue((Boolean) syncResult.get("success"));
    
    // 2. 查询搜索词列表
    Map<String, Object> listResult = searchTermAnalyticsService.getSearchTermAnalyticsList(
        "TEST_ACCOUNT", 123L, null, "2025-01-30", "2025-01-30", 1, 10
    );
    assertTrue((Boolean) listResult.get("success"));
    
    // 3. 获取搜索词详情
    Map<String, Object> detailResult = searchTermAnalyticsService.getSearchTermDetails(
        "TEST_ACCOUNT", 123L, "wireless headphones"
    );
    assertTrue((Boolean) detailResult.get("success"));
}
```

## 10. 测试检查清单

### 10.1 功能测试检查
- [ ] 数据库表创建成功
- [ ] 应用正常启动
- [ ] API接口响应正常
- [ ] 数据同步功能正常
- [ ] 查询功能返回正确数据
- [ ] 分表功能正常工作
- [ ] 定时任务正常执行

### 10.2 性能测试检查
- [ ] 大数据量查询性能可接受
- [ ] 索引使用正确
- [ ] 内存使用正常
- [ ] 响应时间在预期范围内

### 10.3 异常处理检查
- [ ] 参数验证正确
- [ ] 错误信息清晰
- [ ] 异常不会导致系统崩溃
- [ ] 日志记录完整

## 11. 测试报告模板

```
搜索词分析功能测试报告

测试时间：2025-01-31
测试环境：开发环境
测试人员：[姓名]

测试结果：
✅ 数据库功能：通过
✅ API接口：通过  
✅ 数据同步：通过
✅ 查询功能：通过
✅ 分表功能：通过
✅ 定时任务：通过
✅ 性能测试：通过
✅ 异常处理：通过

发现问题：
1. [问题描述]
2. [问题描述]

建议：
1. [改进建议]
2. [改进建议]
```

按照这个测试指南，你可以全面验证搜索词分析功能的正确性和稳定性。
