package com.yiyitech.ads.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yiyitech.ads.mapper.AdsAccountInfoMapper;
import com.yiyitech.ads.mapper.AdsSearchTermAnalyticsMapper;
import com.yiyitech.ads.mapper.AdsSearchTermCategoryRelationMapper;
import com.yiyitech.ads.mapper.AdsSearchTermProductRankingMapper;
import com.yiyitech.ads.model.AdsAccountInfoModel;
import com.yiyitech.ads.model.AdsSearchTermAnalyticsModel;
import com.yiyitech.ads.model.AdsSearchTermCategoryRelationModel;
import com.yiyitech.ads.model.AdsSearchTermProductRankingModel;
import com.yiyitech.ads.model.response.SpAnalyticsReportResponse;
import com.yiyitech.ads.model.response.SpCatalogItemResponse;
import com.yiyitech.ads.service.AdsSearchTermAnalyticsService;
import com.yiyitech.ads.service.apisp.SpAnalyticsReportsApi;
import com.yiyitech.ads.service.apisp.SpServicesApi;
import com.yiyitech.ads.service.apisp.SpTokenApi;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 搜索词分析服务实现类
 * 实现搜索词相关的分析数据处理功能
 * 包括数据同步、查询、分析等核心业务逻辑
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsServiceImpl.java
 * @Description 搜索词分析服务实现类
 * @createTime 2025年01月31日
 */
@Slf4j
@Service
public class AdsSearchTermAnalyticsServiceImpl implements AdsSearchTermAnalyticsService {

    @Autowired
    private AdsSearchTermAnalyticsMapper searchTermAnalyticsMapper;

    @Autowired
    private AdsSearchTermCategoryRelationMapper categoryRelationMapper;

    @Autowired
    private AdsSearchTermProductRankingMapper productRankingMapper;

    @Autowired
    private SpAnalyticsReportsApi spAnalyticsReportsApi;

    @Autowired
    private SpServicesApi spServicesApi;

    @Autowired
    private SpTokenApi spTokenApi;

    @Autowired
    private AdsAccountInfoMapper accountInfoMapper;

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncSearchTermAnalyticsData(String accountId, Long profileId, 
                                                          String marketplaceId, String startDate, String endDate) {
        log.info("开始同步搜索词分析数据 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}", 
                accountId, profileId, marketplaceId, startDate, endDate);
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        
        try {
            // 1. 获取访问令牌
            List<AdsAccountInfoModel> accountInfoList = accountInfoMapper.searchByAccountId(accountId);
            if (CollectionUtil.isEmpty(accountInfoList)) {
                log.error("账户信息不存在 - 账户ID: {}", accountId);
                result.put("success", false);
                result.put("message", "账户信息不存在");
                return result;
            }

            AdsAccountInfoModel accountInfo = accountInfoList.get(0);
            if (StrUtil.isBlank(accountInfo.getRefreshTokenSp())) {
                log.error("SP-API访问令牌为空 - 账户ID: {}", accountId);
                result.put("success", false);
                result.put("message", "SP-API访问令牌为空");
                return result;
            }

            // 这里需要通过refresh token获取access token，暂时使用refresh token
            String accessToken = accountInfo.getRefreshTokenSp();
            List<String> marketplaceIds = new ArrayList<>();
            marketplaceIds.add(marketplaceId);

            // 2. 创建搜索查询性能报告
            SpAnalyticsReportResponse reportResponse = spAnalyticsReportsApi.createSearchQueryPerformanceReport(
                    accessToken, marketplaceIds, startDate, endDate);
            
            if (reportResponse == null || StrUtil.isBlank(reportResponse.getReportId())) {
                log.error("创建搜索查询性能报告失败");
                result.put("success", false);
                result.put("message", "创建搜索查询性能报告失败");
                return result;
            }

            // 3. 等待报告处理完成
            String reportId = reportResponse.getReportId();
            SpAnalyticsReportResponse statusResponse = waitForReportCompletion(accessToken, reportId);
            
            if (statusResponse == null || !statusResponse.isProcessingCompleted()) {
                log.error("报告处理失败或超时 - 报告ID: {}, 状态: {}", 
                        reportId, statusResponse != null ? statusResponse.getProcessingStatus() : "null");
                result.put("success", false);
                result.put("message", "报告处理失败或超时");
                return result;
            }

            // 4. 下载并解析报告数据
            String reportData = spAnalyticsReportsApi.downloadReportData(accessToken, statusResponse.getReportDocumentId());
            List<AdsSearchTermAnalyticsModel> analyticsDataList = parseSearchQueryPerformanceData(
                    reportData, accountId, profileId, marketplaceId);

            // 5. 批量保存数据
            if (CollectionUtil.isNotEmpty(analyticsDataList)) {
                for (AdsSearchTermAnalyticsModel analyticsData : analyticsDataList) {
                    try {
                        // 检查是否已存在相同数据
                        AdsSearchTermAnalyticsModel existingData = searchTermAnalyticsMapper.findBySearchTermAndDate(
                                accountId, profileId, analyticsData.getSearchTerm(), analyticsData.getReportDate());
                        
                        if (existingData != null) {
                            // 更新现有数据
                            analyticsData.setId(existingData.getId());
                            searchTermAnalyticsMapper.updateById(analyticsData);
                        } else {
                            // 插入新数据
                            searchTermAnalyticsMapper.insert(analyticsData);
                        }
                        successCount++;
                    } catch (Exception e) {
                        log.error("保存搜索词分析数据失败 - 搜索词: {}, 错误: {}", 
                                analyticsData.getSearchTerm(), e.getMessage());
                        failCount++;
                    }
                }
            }

            log.info("搜索词分析数据同步完成 - 成功: {}, 失败: {}", successCount, failCount);
            
            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("totalCount", successCount + failCount);
            result.put("message", "数据同步完成");
            
        } catch (Exception e) {
            log.error("同步搜索词分析数据异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "同步异常: " + e.getMessage());
            result.put("successCount", successCount);
            result.put("failCount", failCount);
        }
        
        return result;
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncSearchTermCategoryRelations(String accountId, Long profileId,
                                                              String marketplaceId, String startDate, String endDate) {
        log.info("开始同步搜索词类目关联数据 - 账户: {}, 配置: {}, 市场: {}", accountId, profileId, marketplaceId);
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        
        try {
            // 获取访问令牌
            List<AdsAccountInfoModel> accountInfoList = accountInfoMapper.searchByAccountId(accountId);
            if (CollectionUtil.isEmpty(accountInfoList)) {
                result.put("success", false);
                result.put("message", "账户信息不存在");
                return result;
            }
            String accessToken = accountInfoList.get(0).getRefreshTokenSp();
            List<String> marketplaceIds = new ArrayList<>();
            marketplaceIds.add(marketplaceId);

            // 创建搜索目录性能报告
            SpAnalyticsReportResponse reportResponse = spAnalyticsReportsApi.createSearchCatalogPerformanceReport(
                    accessToken, marketplaceIds, startDate, endDate);
            
            // 等待报告完成并下载数据
            String reportId = reportResponse.getReportId();
            SpAnalyticsReportResponse statusResponse = waitForReportCompletion(accessToken, reportId);
            
            if (statusResponse != null && statusResponse.isProcessingCompleted()) {
                String reportData = spAnalyticsReportsApi.downloadReportData(accessToken, statusResponse.getReportDocumentId());
                List<AdsSearchTermCategoryRelationModel> relationDataList = parseSearchCatalogPerformanceData(
                        reportData, accountId, profileId, marketplaceId);

                // 批量保存类目关联数据
                if (CollectionUtil.isNotEmpty(relationDataList)) {
                    for (AdsSearchTermCategoryRelationModel relationData : relationDataList) {
                        try {
                            // 检查是否已存在
                            AdsSearchTermCategoryRelationModel existingData = categoryRelationMapper.findBySearchTermAndCategory(
                                    accountId, profileId, relationData.getSearchTerm(), relationData.getCategoryId());
                            
                            if (existingData != null) {
                                relationData.setId(existingData.getId());
                                categoryRelationMapper.updateById(relationData);
                            } else {
                                categoryRelationMapper.insert(relationData);
                            }
                            successCount++;
                        } catch (Exception e) {
                            log.error("保存类目关联数据失败: {}", e.getMessage());
                            failCount++;
                        }
                    }
                }
            }

            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            
        } catch (Exception e) {
            log.error("同步搜索词类目关联数据异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        
        return result;
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncSearchTermProductRankings(String accountId, Long profileId,
                                                            String marketplaceId, String startDate, String endDate) {
        log.info("开始同步搜索词商品排名数据 - 账户: {}, 配置: {}, 市场: {}", accountId, profileId, marketplaceId);
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        
        try {
            // 获取访问令牌
            List<AdsAccountInfoModel> accountInfoList = accountInfoMapper.searchByAccountId(accountId);
            if (CollectionUtil.isEmpty(accountInfoList)) {
                result.put("success", false);
                result.put("message", "账户信息不存在");
                return result;
            }
            String accessToken = accountInfoList.get(0).getRefreshTokenSp();

            // 获取搜索词列表（从已有的分析数据中获取）
            List<String> searchTerms = searchTermAnalyticsMapper.getSearchTermsByAccount(accountId, profileId);
            
            for (String searchTerm : searchTerms) {
                try {
                    // 为每个搜索词获取商品排名数据
                    List<AdsSearchTermProductRankingModel> productRankings = fetchProductRankingsForSearchTerm(
                            accessToken, accountId, profileId, marketplaceId, searchTerm);
                    
                    // 保存商品排名数据
                    for (AdsSearchTermProductRankingModel ranking : productRankings) {
                        try {
                            AdsSearchTermProductRankingModel existingData = productRankingMapper.findBySearchTermAndAsin(
                                    accountId, profileId, searchTerm, ranking.getAsin());
                            
                            if (existingData != null) {
                                ranking.setId(existingData.getId());
                                productRankingMapper.updateById(ranking);
                            } else {
                                productRankingMapper.insert(ranking);
                            }
                            successCount++;
                        } catch (Exception e) {
                            log.error("保存商品排名数据失败: {}", e.getMessage());
                            failCount++;
                        }
                    }
                } catch (Exception e) {
                    log.error("获取搜索词 {} 的商品排名数据失败: {}", searchTerm, e.getMessage());
                    failCount++;
                }
            }

            result.put("success", true);
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            
        } catch (Exception e) {
            log.error("同步搜索词商品排名数据异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        
        return result;
    }

    @Override
    @DS("slave")
    public Map<String, Object> getSearchTermAnalyticsList(String accountId, Long profileId, String searchTerm,
                                                         String startDate, String endDate, Integer pageNum, Integer pageSize) {
        log.info("查询搜索词分析数据列表 - 账户: {}, 搜索词: {}", accountId, searchTerm);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建查询条件
            Map<String, Object> params = new HashMap<>();
            params.put("accountId", accountId);
            params.put("profileId", profileId);
            if (StrUtil.isNotBlank(searchTerm)) {
                params.put("searchTerm", searchTerm);
            }
            if (StrUtil.isNotBlank(startDate)) {
                params.put("startDate", startDate);
            }
            if (StrUtil.isNotBlank(endDate)) {
                params.put("endDate", endDate);
            }
            
            // 分页查询
            int offset = (pageNum - 1) * pageSize;
            params.put("offset", offset);
            params.put("limit", pageSize);
            
            List<AdsSearchTermAnalyticsModel> dataList = searchTermAnalyticsMapper.selectByConditions(params);
            int totalCount = searchTermAnalyticsMapper.countByConditions(params);
            
            result.put("success", true);
            result.put("data", dataList);
            result.put("totalCount", totalCount);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("totalPages", (totalCount + pageSize - 1) / pageSize);
            
        } catch (Exception e) {
            log.error("查询搜索词分析数据列表异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        
        return result;
    }

    @Override
    @DS("slave")
    public List<AdsSearchTermCategoryRelationModel> getSearchTermCategories(String accountId, Long profileId, String searchTerm) {
        log.info("查询搜索词关联类目 - 账户: {}, 搜索词: {}", accountId, searchTerm);

        try {
            return categoryRelationMapper.getTopCategoriesBySearchTerm(accountId, profileId, searchTerm, 3);
        } catch (Exception e) {
            log.error("查询搜索词关联类目异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @DS("slave")
    public List<AdsSearchTermProductRankingModel> getSearchTermProductRankings(String accountId, Long profileId, String searchTerm) {
        log.info("查询搜索词关联商品排名 - 账户: {}, 搜索词: {}", accountId, searchTerm);

        try {
            return productRankingMapper.getTopProductsBySearchTerm(accountId, profileId, searchTerm, 3);
        } catch (Exception e) {
            log.error("查询搜索词关联商品排名异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @DS("slave")
    public Map<String, Object> getSearchTermDetails(String accountId, Long profileId, String searchTerm) {
        log.info("查询搜索词详细信息 - 账户: {}, 搜索词: {}", accountId, searchTerm);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取搜索词基本信息
            AdsSearchTermAnalyticsModel analyticsData = searchTermAnalyticsMapper.getLatestBySearchTerm(
                    accountId, profileId, searchTerm);

            // 获取关联类目（前三）
            List<AdsSearchTermCategoryRelationModel> categories = getSearchTermCategories(accountId, profileId, searchTerm);

            // 获取关联商品（前三）
            List<AdsSearchTermProductRankingModel> products = getSearchTermProductRankings(accountId, profileId, searchTerm);

            result.put("success", true);
            result.put("searchTermData", analyticsData);
            result.put("topCategories", categories);
            result.put("topProducts", products);

        } catch (Exception e) {
            log.error("查询搜索词详细信息异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }

        return result;
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSearchTermAnalytics(List<AdsSearchTermAnalyticsModel> searchTermAnalyticsList) {
        log.info("批量更新搜索词分析数据 - 数量: {}", searchTermAnalyticsList.size());

        try {
            for (AdsSearchTermAnalyticsModel analyticsData : searchTermAnalyticsList) {
                searchTermAnalyticsMapper.updateById(analyticsData);
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新搜索词分析数据异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getFirstAvailableAccountId() {
        log.info("获取第一个可用的Amazon账户ID");

        // 硬编码的账户ID列表（从AmazoServiceImpl.setRedis()方法中获取）
        String[] availableAccountIds = {
            "A3QXCPZWLQ2IWO",
            "AF96VBWMIRW5L",
            "A17DJHZT1NEVL4",
            "A1G4JLDUB3JG9",
            "A1HCWIVLDP3LFS"
        };

        // 检查数据库中是否存在这些账户
        for (String accountId : availableAccountIds) {
            try {
                List<AdsAccountInfoModel> accountInfoList = accountInfoMapper.searchByAccountId(accountId);
                if (CollectionUtil.isNotEmpty(accountInfoList)) {
                    AdsAccountInfoModel accountInfo = accountInfoList.get(0);
                    if (accountInfo.getStatus() && StrUtil.isNotBlank(accountInfo.getRefreshTokenSp())) {
                        log.info("找到可用账户: {}", accountId);
                        return accountId;
                    }
                }
            } catch (Exception e) {
                log.warn("检查账户 {} 时出错: {}", accountId, e.getMessage());
            }
        }

        // 如果数据库中没有找到，返回第一个硬编码账户ID
        log.info("数据库中未找到可用账户，使用默认账户: {}", availableAccountIds[0]);
        return availableAccountIds[0];
    }

    /**
     * 获取账户的第一个可用profileId
     *
     * @param accountId 账户ID
     * @return profileId
     */
    public Long getFirstAvailableProfileId(String accountId) {
        log.info("获取账户 {} 的第一个可用profileId", accountId);

        try {
            // 先尝试从数据库获取
            List<AdsAccountInfoModel> accountInfoList = accountInfoMapper.searchByAccountId(accountId);
            if (CollectionUtil.isNotEmpty(accountInfoList)) {
                // 这里可以添加从数据库获取profileId的逻辑
                // 暂时返回硬编码的真实profileId
            }
        } catch (Exception e) {
            log.warn("从数据库获取profileId失败: {}", e.getMessage());
        }

        // 返回硬编码的真实profileId（从AmazoServiceImpl中获取）
        Long defaultProfileId = 1762485261961949L;
        log.info("使用默认profileId: {}", defaultProfileId);
        return defaultProfileId;
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSearchTermCategoryRelations(List<AdsSearchTermCategoryRelationModel> categoryRelationList) {
        log.info("批量更新搜索词类目关联数据 - 数量: {}", categoryRelationList.size());

        try {
            for (AdsSearchTermCategoryRelationModel relationData : categoryRelationList) {
                categoryRelationMapper.updateById(relationData);
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新搜索词类目关联数据异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSearchTermProductRankings(List<AdsSearchTermProductRankingModel> productRankingList) {
        log.info("批量更新搜索词商品排名数据 - 数量: {}", productRankingList.size());

        try {
            for (AdsSearchTermProductRankingModel rankingData : productRankingList) {
                productRankingMapper.updateById(rankingData);
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新搜索词商品排名数据异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @DS("slave")
    public List<AdsSearchTermAnalyticsModel> getTopSearchTerms(String accountId, Long profileId, Integer limit) {
        log.info("查询热门搜索词 - 账户: {}, 限制: {}", accountId, limit);

        try {
            return searchTermAnalyticsMapper.getTopSearchTermsByVolume(accountId, profileId, limit);
        } catch (Exception e) {
            log.error("查询热门搜索词异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    @DS("slave")
    public Map<String, Object> getSearchTermTrends(String accountId, Long profileId, String searchTerm,
                                                   String startDate, String endDate) {
        log.info("查询搜索词趋势数据 - 账户: {}, 搜索词: {}", accountId, searchTerm);

        Map<String, Object> result = new HashMap<>();

        try {
            List<AdsSearchTermAnalyticsModel> trendData = searchTermAnalyticsMapper.getSearchTermTrends(
                    accountId, profileId, searchTerm, startDate, endDate);

            // 计算趋势指标
            Map<String, Object> trendAnalysis = analyzeTrendData(trendData);

            result.put("success", true);
            result.put("trendData", trendData);
            result.put("trendAnalysis", trendAnalysis);

        } catch (Exception e) {
            log.error("查询搜索词趋势数据异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }

        return result;
    }

    @Override
    @DS("slave")
    public Map<String, Object> analyzeSearchTermCompetition(String accountId, Long profileId, String searchTerm) {
        log.info("分析搜索词竞争情况 - 账户: {}, 搜索词: {}", accountId, searchTerm);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取搜索词基本数据
            AdsSearchTermAnalyticsModel analyticsData = searchTermAnalyticsMapper.getLatestBySearchTerm(
                    accountId, profileId, searchTerm);

            if (analyticsData == null) {
                result.put("success", false);
                result.put("message", "搜索词数据不存在");
                return result;
            }

            // 分析竞争强度
            Map<String, Object> competitionAnalysis = new HashMap<>();
            competitionAnalysis.put("competitionLevel", getCompetitionLevel(analyticsData.getCompetitionIntensity()));
            competitionAnalysis.put("searchVolume", analyticsData.getMonthlySearchVolume());
            competitionAnalysis.put("growthRate", analyticsData.getSearchVolumeGrowthRate());
            competitionAnalysis.put("averageCPC", analyticsData.getAverageCostPerClick());

            // 获取竞争商品数量
            List<AdsSearchTermProductRankingModel> competingProducts = productRankingMapper.getProductsBySearchTerm(
                    accountId, profileId, searchTerm);
            competitionAnalysis.put("competingProductsCount", competingProducts.size());

            // 计算机会得分
            double opportunityScore = calculateOpportunityScore(analyticsData, competingProducts.size());
            competitionAnalysis.put("opportunityScore", opportunityScore);

            result.put("success", true);
            result.put("searchTerm", searchTerm);
            result.put("competitionAnalysis", competitionAnalysis);

        } catch (Exception e) {
            log.error("分析搜索词竞争情况异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }

        return result;
    }

    @Override
    @DS("slave")
    public List<String> getSearchTermSuggestions(String accountId, Long profileId, String seedKeyword, Integer limit) {
        log.info("获取搜索词建议 - 账户: {}, 种子词: {}, 限制: {}", accountId, seedKeyword, limit);

        try {
            return searchTermAnalyticsMapper.getRelatedSearchTerms(accountId, profileId, seedKeyword, limit);
        } catch (Exception e) {
            log.error("获取搜索词建议异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 等待报告处理完成
     *
     * @param accessToken 访问令牌
     * @param reportId 报告ID
     * @return 报告状态响应
     */
    private SpAnalyticsReportResponse waitForReportCompletion(String accessToken, String reportId) {
        int maxAttempts = 30; // 最多等待30次，每次10秒
        int attemptCount = 0;

        while (attemptCount < maxAttempts) {
            try {
                SpAnalyticsReportResponse statusResponse = spAnalyticsReportsApi.getReportStatus(accessToken, reportId);

                if (statusResponse.isProcessingCompleted()) {
                    log.info("报告处理完成 - 报告ID: {}", reportId);
                    return statusResponse;
                } else if (statusResponse.isProcessingFailed()) {
                    log.error("报告处理失败 - 报告ID: {}, 状态: {}", reportId, statusResponse.getProcessingStatus());
                    return statusResponse;
                }

                // 等待10秒后重试
                Thread.sleep(10000);
                attemptCount++;

            } catch (Exception e) {
                log.error("检查报告状态异常 - 报告ID: {}, 错误: {}", reportId, e.getMessage());
                attemptCount++;
            }
        }

        log.error("报告处理超时 - 报告ID: {}", reportId);
        return null;
    }

    /**
     * 解析搜索查询性能报告数据
     *
     * @param reportData 报告数据JSON字符串
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @return 搜索词分析数据列表
     */
    private List<AdsSearchTermAnalyticsModel> parseSearchQueryPerformanceData(String reportData,
                                                                             String accountId, Long profileId, String marketplaceId) {
        List<AdsSearchTermAnalyticsModel> result = new ArrayList<>();

        try {
            JSONObject jsonData = JSONUtil.parseObj(reportData);
            JSONArray dataArray = jsonData.getJSONArray("data");

            if (dataArray != null) {
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);

                    AdsSearchTermAnalyticsModel analyticsData = new AdsSearchTermAnalyticsModel();
                    analyticsData.setAccountId(accountId);
                    analyticsData.setProfileId(profileId);
                    analyticsData.setMarketplaceId(marketplaceId);
                    analyticsData.setSearchTerm(item.getStr("searchTerm"));
                    analyticsData.setSearchRank(item.getInt("searchRank"));
                    analyticsData.setMonthlySearchVolume(item.getLong("searchVolume"));
                    analyticsData.setClicks(item.getInt("clicks"));
                    analyticsData.setImpressions(item.getInt("impressions"));
                    analyticsData.setClickThroughRate(item.getDouble("clickThroughRate"));
                    analyticsData.setConversionRate(item.getDouble("conversionRate"));
                    analyticsData.setAverageCostPerClick(item.getDouble("averageCostPerClick"));
                    analyticsData.setSearchFrequencyRank(item.getInt("searchFrequencyRank"));
                    analyticsData.setCompetitionIntensity(item.getInt("competitionIntensity"));
                    analyticsData.setRelevanceScore(item.getDouble("relevanceScore"));
                    analyticsData.setDataSource("SP_API_SEARCH_QUERY_PERFORMANCE");
                    analyticsData.setStatus("ACTIVE");
                    analyticsData.setReportDate(new Date());

                    // 计算环比增长率
                    Long previousVolume = item.getLong("previousMonthSearchVolume");
                    if (previousVolume != null) {
                        analyticsData.setPreviousMonthSearchVolume(previousVolume);
                        analyticsData.setSearchVolumeGrowthRate(analyticsData.calculateGrowthRate());
                    }

                    result.add(analyticsData);
                }
            }

        } catch (Exception e) {
            log.error("解析搜索查询性能报告数据异常: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 解析搜索目录性能报告数据
     *
     * @param reportData 报告数据JSON字符串
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @return 搜索词类目关联数据列表
     */
    private List<AdsSearchTermCategoryRelationModel> parseSearchCatalogPerformanceData(String reportData,
                                                                                      String accountId, Long profileId, String marketplaceId) {
        List<AdsSearchTermCategoryRelationModel> result = new ArrayList<>();

        try {
            JSONObject jsonData = JSONUtil.parseObj(reportData);
            JSONArray dataArray = jsonData.getJSONArray("data");

            if (dataArray != null) {
                // 按搜索词分组数据
                Map<String, List<JSONObject>> searchTermGroups = new HashMap<>();

                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);
                    String searchTerm = item.getStr("searchTerm");

                    searchTermGroups.computeIfAbsent(searchTerm, k -> new ArrayList<>()).add(item);
                }

                // 为每个搜索词处理类目数据
                for (Map.Entry<String, List<JSONObject>> entry : searchTermGroups.entrySet()) {
                    String searchTerm = entry.getKey();
                    List<JSONObject> categoryItems = entry.getValue();

                    // 按点击量排序，取前三
                    categoryItems.sort((a, b) -> Integer.compare(
                            b.getInt("clicksInCategory", 0),
                            a.getInt("clicksInCategory", 0)));

                    for (int rank = 0; rank < Math.min(3, categoryItems.size()); rank++) {
                        JSONObject item = categoryItems.get(rank);

                        AdsSearchTermCategoryRelationModel relationData = new AdsSearchTermCategoryRelationModel();
                        relationData.setAccountId(accountId);
                        relationData.setProfileId(profileId);
                        relationData.setMarketplaceId(marketplaceId);
                        relationData.setSearchTerm(searchTerm);
                        relationData.setCategoryId(item.getLong("categoryId"));
                        relationData.setCategoryName(item.getStr("categoryName"));
                        relationData.setParentCategoryId(item.getLong("parentCategoryId"));
                        relationData.setCategoryLevel(item.getInt("categoryLevel"));
                        relationData.setCategoryPath(item.getStr("categoryPath"));
                        relationData.setClicksInCategory(item.getInt("clicksInCategory"));
                        relationData.setImpressionsInCategory(item.getInt("impressionsInCategory"));
                        relationData.setConversionsInCategory(item.getInt("conversionsInCategory"));
                        relationData.setSalesInCategory(item.getDouble("salesInCategory"));
                        relationData.setClickRank(rank + 1);
                        relationData.setIsPrimaryCategory(rank < 3);
                        relationData.setDataSource("SP_API_SEARCH_CATALOG_PERFORMANCE");
                        relationData.setStatus("ACTIVE");
                        relationData.setStatisticsDate(new Date());

                        // 计算关联强度得分
                        relationData.setRelevanceScore(relationData.calculateRelevanceScore());

                        result.add(relationData);
                    }
                }
            }

        } catch (Exception e) {
            log.error("解析搜索目录性能报告数据异常: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 为指定搜索词获取商品排名数据
     *
     * @param accessToken 访问令牌
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param searchTerm 搜索词
     * @return 商品排名数据列表
     */
    private List<AdsSearchTermProductRankingModel> fetchProductRankingsForSearchTerm(String accessToken,
                                                                                    String accountId, Long profileId,
                                                                                    String marketplaceId, String searchTerm) {
        List<AdsSearchTermProductRankingModel> result = new ArrayList<>();

        try {
            // 这里需要调用Amazon Catalog API来获取搜索词相关的商品信息
            // 由于SP-API没有直接的搜索词商品排名API，我们需要通过其他方式获取
            // 比如通过现有的搜索词报告数据中的ASIN信息，然后调用Catalog API获取商品详情

            // 从现有数据中获取与该搜索词相关的ASIN列表
            List<String> relatedAsins = getRelatedAsinsForSearchTerm(accountId, profileId, searchTerm);

            int rank = 1;
            for (String asin : relatedAsins) {
                if (rank > 3) break; // 只取前三

                try {
                    // 调用SP-API获取商品详情
                    SpCatalogItemResponse catalogItem = spServicesApi.getSpCatalogItem(asin, marketplaceId, accessToken);

                    if (catalogItem != null) {
                        AdsSearchTermProductRankingModel productRanking = new AdsSearchTermProductRankingModel();
                        productRanking.setAccountId(accountId);
                        productRanking.setProfileId(profileId);
                        productRanking.setMarketplaceId(marketplaceId);
                        productRanking.setSearchTerm(searchTerm);
                        productRanking.setAsin(asin);
                        productRanking.setClickRank(rank);
                        productRanking.setDataSource("SP_API_CATALOG_ITEMS");
                        productRanking.setStatus("ACTIVE");
                        productRanking.setStatisticsDate(new Date());

                        // 从catalog item中提取商品信息
                        extractProductInfoFromCatalogItem(productRanking, catalogItem);

                        result.add(productRanking);
                        rank++;
                    }
                } catch (Exception e) {
                    log.error("获取ASIN {} 的商品信息失败: {}", asin, e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("获取搜索词 {} 的商品排名数据异常: {}", searchTerm, e.getMessage());
        }

        return result;
    }

    /**
     * 获取与搜索词相关的ASIN列表
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return ASIN列表
     */
    private List<String> getRelatedAsinsForSearchTerm(String accountId, Long profileId, String searchTerm) {
        // 这里可以从现有的搜索词报告数据中获取相关的ASIN
        // 或者从其他数据源获取
        return new ArrayList<>(); // 暂时返回空列表，实际实现需要根据具体数据源
    }

    /**
     * 从Catalog Item中提取商品信息
     *
     * @param productRanking 商品排名模型
     * @param catalogItem Catalog Item响应
     */
    private void extractProductInfoFromCatalogItem(AdsSearchTermProductRankingModel productRanking, Object catalogItem) {
        // 这里需要根据实际的Catalog Item响应结构来提取信息
        // 包括商品标题、图片URL、价格、品牌等信息
        // 由于SpCatalogItemResponse的具体结构需要查看，这里先留空实现
    }

    /**
     * 分析趋势数据
     *
     * @param trendData 趋势数据列表
     * @return 趋势分析结果
     */
    private Map<String, Object> analyzeTrendData(List<AdsSearchTermAnalyticsModel> trendData) {
        Map<String, Object> analysis = new HashMap<>();

        if (CollectionUtil.isEmpty(trendData)) {
            return analysis;
        }

        // 计算平均搜索量
        double avgSearchVolume = trendData.stream()
                .filter(data -> data.getMonthlySearchVolume() != null)
                .mapToLong(AdsSearchTermAnalyticsModel::getMonthlySearchVolume)
                .average()
                .orElse(0.0);

        // 计算平均点击率
        double avgClickThroughRate = trendData.stream()
                .filter(data -> data.getClickThroughRate() != null)
                .mapToDouble(AdsSearchTermAnalyticsModel::getClickThroughRate)
                .average()
                .orElse(0.0);

        // 计算趋势方向
        String trendDirection = calculateTrendDirection(trendData);

        analysis.put("averageSearchVolume", avgSearchVolume);
        analysis.put("averageClickThroughRate", avgClickThroughRate);
        analysis.put("trendDirection", trendDirection);
        analysis.put("dataPoints", trendData.size());

        return analysis;
    }

    /**
     * 计算趋势方向
     *
     * @param trendData 趋势数据
     * @return 趋势方向（UP, DOWN, STABLE）
     */
    private String calculateTrendDirection(List<AdsSearchTermAnalyticsModel> trendData) {
        if (trendData.size() < 2) {
            return "STABLE";
        }

        // 按日期排序
        trendData.sort(Comparator.comparing(AdsSearchTermAnalyticsModel::getReportDate));

        // 比较首尾数据
        AdsSearchTermAnalyticsModel first = trendData.get(0);
        AdsSearchTermAnalyticsModel last = trendData.get(trendData.size() - 1);

        if (first.getMonthlySearchVolume() != null && last.getMonthlySearchVolume() != null) {
            long firstVolume = first.getMonthlySearchVolume();
            long lastVolume = last.getMonthlySearchVolume();

            double changeRate = ((double) (lastVolume - firstVolume) / firstVolume) * 100;

            if (changeRate > 10) {
                return "UP";
            } else if (changeRate < -10) {
                return "DOWN";
            }
        }

        return "STABLE";
    }

    /**
     * 获取竞争等级描述
     *
     * @param competitionIntensity 竞争强度
     * @return 竞争等级描述
     */
    private String getCompetitionLevel(Integer competitionIntensity) {
        if (competitionIntensity == null) {
            return "未知";
        }

        switch (competitionIntensity) {
            case 1:
                return "低竞争";
            case 2:
                return "中等竞争";
            case 3:
                return "高竞争";
            default:
                return "未知";
        }
    }

    /**
     * 计算机会得分
     *
     * @param analyticsData 分析数据
     * @param competingProductsCount 竞争商品数量
     * @return 机会得分（0-100）
     */
    private double calculateOpportunityScore(AdsSearchTermAnalyticsModel analyticsData, int competingProductsCount) {
        double score = 0.0;

        // 搜索量得分（权重40%）
        if (analyticsData.getMonthlySearchVolume() != null) {
            double volumeScore = Math.min(analyticsData.getMonthlySearchVolume() / 10000.0 * 40, 40);
            score += volumeScore;
        }

        // 增长率得分（权重30%）
        if (analyticsData.getSearchVolumeGrowthRate() != null) {
            double growthScore = Math.max(0, Math.min(analyticsData.getSearchVolumeGrowthRate() / 100.0 * 30, 30));
            score += growthScore;
        }

        // 竞争强度得分（权重20%，竞争越低得分越高）
        if (analyticsData.getCompetitionIntensity() != null) {
            double competitionScore = (4 - analyticsData.getCompetitionIntensity()) / 3.0 * 20;
            score += competitionScore;
        }

        // 竞争商品数量得分（权重10%，商品数量越少得分越高）
        double productCountScore = Math.max(0, (100 - competingProductsCount) / 100.0 * 10);
        score += productCountScore;

        return Math.min(score, 100.0);
    }
}
