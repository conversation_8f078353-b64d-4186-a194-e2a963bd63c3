package com.yiyitech.ads.controller;

import cn.hutool.core.util.StrUtil;
import com.yiyitech.ads.job.AdsSearchTermAnalyticsTask;
import com.yiyitech.ads.model.AdsSearchTermAnalyticsModel;
import com.yiyitech.ads.model.AdsSearchTermCategoryRelationModel;
import com.yiyitech.ads.model.AdsSearchTermProductRankingModel;
import com.yiyitech.ads.service.AdsSearchTermAnalyticsService;
import com.yiyitech.ads.service.AmazoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 搜索词分析控制器
 * 提供搜索词分析相关的API接口
 * 包括数据查询、同步、分析等功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsController.java
 * @Description 搜索词分析控制器
 * @createTime 2025年01月31日
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ads/search-term-analytics")
public class AdsSearchTermAnalyticsController {

    @Autowired
    private AdsSearchTermAnalyticsService searchTermAnalyticsService;

    @Autowired
    private AdsSearchTermAnalyticsTask searchTermAnalyticsTask;

    @Autowired
    private AmazoService amazoService;

    /**
     * 获取搜索词分析数据列表
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词（可选）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageNum 页码（默认1）
     * @param pageSize 页大小（默认20）
     * @return 搜索词分析数据列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getSearchTermAnalyticsList(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam(required = false) String searchTerm,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {
        
        log.info("查询搜索词分析数据列表 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermAnalyticsList(
                    accountId, profileId, searchTerm, startDate, endDate, pageNum, pageSize);
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词分析数据列表异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词详细信息
     * 包含搜索词基本信息、关联类目、关联商品等
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 搜索词详细信息
     */
    @GetMapping("/details")
    public ResponseEntity<Map<String, Object>> getSearchTermDetails(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("查询搜索词详细信息 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermDetails(
                    accountId, profileId, searchTerm);
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词详细信息异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词关联的类目数据
     * 返回点击量前三的类目
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 关联类目列表
     */
    @GetMapping("/categories")
    public ResponseEntity<Map<String, Object>> getSearchTermCategories(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("查询搜索词关联类目 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            List<AdsSearchTermCategoryRelationModel> categories = 
                    searchTermAnalyticsService.getSearchTermCategories(accountId, profileId, searchTerm);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", categories);
            result.put("count", categories.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词关联类目异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词关联的商品排名数据
     * 返回点击量前三的商品
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 商品排名列表
     */
    @GetMapping("/products")
    public ResponseEntity<Map<String, Object>> getSearchTermProductRankings(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("查询搜索词关联商品排名 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            List<AdsSearchTermProductRankingModel> products = 
                    searchTermAnalyticsService.getSearchTermProductRankings(accountId, profileId, searchTerm);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", products);
            result.put("count", products.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词关联商品排名异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取热门搜索词列表
     * 按搜索量排序
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param limit 返回数量限制（默认10）
     * @return 热门搜索词列表
     */
    @GetMapping("/top-search-terms")
    public ResponseEntity<Map<String, Object>> getTopSearchTerms(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("查询热门搜索词 - 账户: {}, 配置: {}, 限制: {}", accountId, profileId, limit);
        
        try {
            List<AdsSearchTermAnalyticsModel> topSearchTerms = 
                    searchTermAnalyticsService.getTopSearchTerms(accountId, profileId, limit);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", topSearchTerms);
            result.put("count", topSearchTerms.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询热门搜索词异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词趋势数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据
     */
    @GetMapping("/trends")
    public ResponseEntity<Map<String, Object>> getSearchTermTrends(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        
        log.info("查询搜索词趋势数据 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermTrends(
                    accountId, profileId, searchTerm, startDate, endDate);
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词趋势数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 分析搜索词竞争情况
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 竞争分析结果
     */
    @GetMapping("/competition-analysis")
    public ResponseEntity<Map<String, Object>> analyzeSearchTermCompetition(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("分析搜索词竞争情况 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            Map<String, Object> result = searchTermAnalyticsService.analyzeSearchTermCompetition(
                    accountId, profileId, searchTerm);
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("分析搜索词竞争情况异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "分析异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词建议
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param seedKeyword 种子关键词
     * @param limit 返回数量限制（默认10）
     * @return 搜索词建议列表
     */
    @GetMapping("/suggestions")
    public ResponseEntity<Map<String, Object>> getSearchTermSuggestions(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String seedKeyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("获取搜索词建议 - 账户: {}, 配置: {}, 种子词: {}", accountId, profileId, seedKeyword);
        
        try {
            List<String> suggestions = searchTermAnalyticsService.getSearchTermSuggestions(
                    accountId, profileId, seedKeyword, limit);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", suggestions);
            result.put("count", suggestions.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("获取搜索词建议异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动同步搜索词分析数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/analytics")
    public ResponseEntity<Map<String, Object>> syncSearchTermAnalyticsData(
            @RequestParam(required = false) String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动同步搜索词分析数据 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                accountId, profileId, marketplaceId, startDate, endDate);

        try {
            // 如果没有提供accountId，自动获取第一个可用账户
            if (StrUtil.isBlank(accountId)) {
                accountId = searchTermAnalyticsService.getFirstAvailableAccountId();
                if (StrUtil.isBlank(accountId)) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("message", "没有找到可用的Amazon账户");
                    return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
                }
                log.info("自动选择账户: {}", accountId);
            }

            // 参数验证
            if (profileId == null || StrUtil.isBlank(marketplaceId) ||
                StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "参数不能为空");
                return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
            }

            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermAnalyticsData(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动同步搜索词分析数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动同步搜索词类目关联数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/categories")
    public ResponseEntity<Map<String, Object>> syncSearchTermCategoryRelations(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动同步搜索词类目关联数据 - 账户: {}, 配置: {}, 市场: {}", accountId, profileId, marketplaceId);

        try {
            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermCategoryRelations(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动同步搜索词类目关联数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动同步搜索词商品排名数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/products")
    public ResponseEntity<Map<String, Object>> syncSearchTermProductRankings(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动同步搜索词商品排名数据 - 账户: {}, 配置: {}, 市场: {}", accountId, profileId, marketplaceId);

        try {
            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermProductRankings(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动同步搜索词商品排名数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动触发完整数据同步
     * 包括搜索词分析、类目关联、商品排名数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/all")
    public ResponseEntity<Map<String, Object>> syncAllSearchTermData(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动触发完整数据同步 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                accountId, profileId, marketplaceId, startDate, endDate);

        try {
            Map<String, Object> result = searchTermAnalyticsTask.manualSyncSearchTermData(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动触发完整数据同步异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取数据同步状态
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 同步状态信息
     */
    @GetMapping("/sync/status")
    public ResponseEntity<Map<String, Object>> getSyncStatus(
            @RequestParam String accountId,
            @RequestParam Long profileId) {

        log.info("查询数据同步状态 - 账户: {}, 配置: {}", accountId, profileId);

        try {
            // 这里可以实现同步状态查询逻辑
            // 例如查询最近的同步时间、同步数量等
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "同步状态查询功能待实现");

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("查询数据同步状态异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 快速测试同步 - 使用默认参数和自动选择账户
     *
     * @return 同步结果
     */
    @PostMapping("/sync/test")
    public ResponseEntity<Map<String, Object>> testSyncWithDefaults() {
        log.info("快速测试同步 - 使用默认参数");

        try {
            // 自动获取第一个可用账户
            String accountId = searchTermAnalyticsService.getFirstAvailableAccountId();
            if (StrUtil.isBlank(accountId)) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "没有找到可用的Amazon账户");
                return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
            }

            // 使用默认参数
            Long profileId = 123L;
            String marketplaceId = "ATVPDKIKX0DER";
            String startDate = "2025-01-30";
            String endDate = "2025-01-30";

            log.info("使用参数 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                    accountId, profileId, marketplaceId, startDate, endDate);

            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermAnalyticsData(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("快速测试同步异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 初始化Redis中的Amazon账户token
     * 用于解决token过期问题
     *
     * @return 初始化结果
     */
    @PostMapping("/init/tokens")
    public ResponseEntity<Map<String, Object>> initializeTokens() {
        log.info("初始化Redis中的Amazon账户token");

        try {
            // 调用AmazoService的setRedis方法来初始化token
            amazoService.setRedis();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Amazon账户token初始化成功");

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("初始化token异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "初始化token异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
